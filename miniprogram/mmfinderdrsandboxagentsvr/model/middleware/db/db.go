// Package db 定义相关的数据库类型
package db

import (
	"time"

	"gorm.io/datatypes"
)

// AIMiniProgramSandbox 定义相关的数据库类型
type AIMiniProgramSandbox struct {
	ID               int64     `gorm:"primaryKey;column:id;not null;autoIncrement"`
	Username         string    `gorm:"column:username;not null;unique"`
	Password         string    `gorm:"column:password;not null"`
	Addr             string    `gorm:"column:addr;not null"`
	HeartbeatedAt    time.Time `gorm:"column:heartbeated_at;not null;autoCreateTime;default:CURRENT_TIMESTAMP"`
	CliHeartbeatedAt time.Time `gorm:"column:cli_heartbeated_at;not null;autoCreateTime;default:CURRENT_TIMESTAMP"`
	CreatedAt        time.Time `gorm:"column:created_at;not null;autoCreateTime;default:CURRENT_TIMESTAMP"`
	UpdatedAt        time.Time `gorm:"column:updated_at;not null;autoUpdateTime;default:CURRENT_TIMESTAMP"`
}

// TableName 定义相关的数据库类型对应的表名
func (a *AIMiniProgramSandbox) TableName() string {
	return "ai_miniprogram_sandbox"
}

// AIMiniProgramAnnotation 小程序标注记录主表
type AIMiniProgramAnnotation struct {
	ID          int64     `gorm:"primaryKey;column:id;not null;autoIncrement"`
	Rtx         string    `gorm:"column:rtx;not null;size:100"`
	AppID       string    `gorm:"column:app_id;not null;size:100"`
	UserID      string    `gorm:"column:user_id;not null;size:100"`
	TargetID    string    `gorm:"column:target_id;not null;size:100"`
	Instruction string    `gorm:"column:instruction;not null;type:text"`
	SubmitTime  time.Time `gorm:"column:submitted_at;not null"`
	IsDeleted   int8      `gorm:"column:is_deleted;not null;default:0;type:tinyint"` // 0表示未删除，1表示已删除
	IsEval      int8      `gorm:"column:is_eval;not null;default:0;type:tinyint"`    // 0表示训练数据，1表示评估数据
	Source      int8      `gorm:"column:source;not null;default:0;type:tinyint"`     // 0表示默认来源，1表示其他来源
	CreatedAt   time.Time `gorm:"column:created_at;not null;autoCreateTime;default:CURRENT_TIMESTAMP"`
	UpdatedAt   time.Time `gorm:"column:updated_at;not null;autoUpdateTime;default:CURRENT_TIMESTAMP"`
}

// TableName 定义相关的数据库类型对应的表名
func (a *AIMiniProgramAnnotation) TableName() string {
	return "ai_miniprogram_annotation"
}

// AIMiniProgramAnnotationOperation 小程序标注操作记录表
type AIMiniProgramAnnotationOperation struct {
	ID               int64          `gorm:"primaryKey;column:id;not null;autoIncrement"`
	AnnotationID     int64          `gorm:"column:annotation_id;not null"`
	Type             string         `gorm:"column:type;not null;size:20"`
	Text             string         `gorm:"column:text;type:text"`
	Direction        string         `gorm:"column:direction;size:10"`
	Length           int            `gorm:"column:length"`
	URL              string         `gorm:"column:url;type:text"`
	DomXML           string         `gorm:"column:dom_xml;type:text"`
	AllElementsRects string         `gorm:"column:all_elements_rects;type:text"`
	ScreenWidth      int            `gorm:"column:screen_width"`
	ScreenHeight     int            `gorm:"column:screen_height"`
	MarkConfig       datatypes.JSON `gorm:"column:mark_config;type:jsonb"`
	ExtraInfo        datatypes.JSON `gorm:"column:extra_info;type:jsonb"`
	IsDeleted        int8           `gorm:"column:is_deleted;not null;default:0;type:tinyint"` // 0表示未删除，1表示已删除
	CreatedAt        time.Time      `gorm:"column:created_at;not null;autoCreateTime;default:CURRENT_TIMESTAMP"`
}

// TableName 定义相关的数据库类型对应的表名
func (a *AIMiniProgramAnnotationOperation) TableName() string {
	return "ai_miniprogram_annotation_operation"
}
