// Package sandbox 定义相关的接口
package sandbox

import (
	"fmt"
	"mmfinderdrsandboxagentsvr/model/api/base"
	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"
	sandbox_service "mmfinderdrsandboxagentsvr/service/sandbox"
	"mmfinderdrsandboxagentsvr/util"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

// createSandbox 创建沙箱接口
func createSandbox(ctx iris.Context) {
	req := &sandbox_api_model.CreateSandboxReq{}
	resp := &base.Resp[sandbox_api_model.CreateSandboxRespData]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	if err := util.ExecuteFunctionInQueueWithTimeout(time.Second*45, func() {
		var err error
		resp, err = sandbox_service.CreateSandbox(ctx, req)
		if err != nil {
			resp = &base.Resp[sandbox_api_model.CreateSandboxRespData]{}
			resp.SetErr(ctx, iris.StatusInternalServerError, err)
			_ = ctx.JSON(resp)
			return
		}
		_ = ctx.JSON(resp)
	}); err != nil {
		// 如果被锁 block 住太久则直接返回
		resp := &base.Resp[sandbox_api_model.CreateSandboxRespData]{}
		resp.SetErr(ctx, iris.StatusTooManyRequests, fmt.Errorf("系统繁忙，请稍后再试"))
		_ = ctx.JSON(resp)
		return
	}

}

// sandboxAction 沙箱交互步骤接口
func sandboxAction(ctx iris.Context) {
	req := &sandbox_api_model.ActionReq{}
	resp := &base.Resp[sandbox_api_model.ActionRespData]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	if err := util.ExecuteFunctionInQueueWithTimeout(time.Second*45, func() {
		var err error
		resp, err = sandbox_service.ExecuteSandboxAction(ctx, req)
		if err != nil {
			resp = &base.Resp[sandbox_api_model.ActionRespData]{}
			resp.SetErr(ctx, iris.StatusInternalServerError, err)
			_ = ctx.JSON(resp)
			return
		}
		_ = ctx.JSON(resp)
	}); err != nil {
		// 如果被锁 block 住太久则直接返回
		resp := &base.Resp[sandbox_api_model.ActionRespData]{}
		resp.SetErr(ctx, iris.StatusTooManyRequests, fmt.Errorf("系统繁忙，请稍后再试"))
		_ = ctx.JSON(resp)
		return
	}

}

// killSandbox 关闭沙箱接口
func killSandbox(ctx iris.Context) {
	req := &sandbox_api_model.KillSandboxReq{}
	resp := &base.Resp[any]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	if err := util.ExecuteFunctionInQueueWithTimeout(time.Second*45, func() {
		var err error
		resp, err = sandbox_service.KillSandbox(ctx, req)
		if err != nil {
			resp = &base.Resp[any]{}
			// if error starts with "等待 XWeb 回调事件消息超时", it means sandbox has already been killed
			if strings.HasPrefix(err.Error(), "等待 XWeb 回调事件消息超时") {
				resp.Message = "success"
				_ = ctx.JSON(resp)
				return
			}
			resp.SetErr(ctx, iris.StatusInternalServerError, err)
			_ = ctx.JSON(resp)
			return
		}
		_ = ctx.JSON(resp)
	}); err != nil {
		// 如果被锁 block 住太久则直接返回
		resp := &base.Resp[any]{}
		resp.SetErr(ctx, iris.StatusTooManyRequests, fmt.Errorf("系统繁忙，请稍后再试"))
		_ = ctx.JSON(resp)
		return
	}

}

// submitAnnotation 提交标注内容接口
func submitAnnotation(ctx iris.Context) {
	req := &sandbox_api_model.SubmitAnnotationReq{}
	resp := &base.Resp[any]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	resp, err := sandbox_service.SubmitAnnotation(req)
	if err != nil {
		resp = &base.Resp[any]{}
		resp.SetErr(ctx, iris.StatusInternalServerError, err)
		_ = ctx.JSON(resp)
		return
	}

	_ = ctx.JSON(resp)
}

// getAnnotations 获取标注内容列表接口
func getAnnotations(ctx iris.Context) {
	// 获取rtx参数，支持逗号分隔的多个值: rtx=user1,user2,user3
	rtxValues := make([]string, 0)

	// 获取rtx参数
	rtxParam := ctx.URLParam("rtx")

	// 如果参数包含逗号，则按逗号分割
	if strings.Contains(rtxParam, ",") {
		// 分割并添加到rtxValues
		values := strings.Split(rtxParam, ",")
		for _, v := range values {
			trimmed := strings.TrimSpace(v)
			if trimmed != "" {
				rtxValues = append(rtxValues, trimmed)
			}
		}
	} else if rtxParam != "" {
		// 单个值直接添加
		rtxValues = append(rtxValues, rtxParam)
	}

	appID := ctx.URLParam("appId")
	targetID := ctx.URLParam("targetId")
	instruction := ctx.URLParam("instruction")

	limitStr := ctx.URLParam("limit")
	limit := 10 // 默认值
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil {
			limit = parsedLimit
		}
	}

	offsetStr := ctx.URLParam("offset")
	offset := 0 // 默认值
	if offsetStr != "" {
		if parsedOffset, err := strconv.Atoi(offsetStr); err == nil {
			offset = parsedOffset
		}
	}

	isDeletedStr := ctx.URLParam("isDeleted")
	var isDeleted *int8 // 默认为nil，表示不筛选
	if isDeletedStr != "" {
		if parsedIsDeleted, err := strconv.Atoi(isDeletedStr); err == nil {
			val := int8(parsedIsDeleted)
			isDeleted = &val
		}
	}

	isEvalStr := ctx.URLParam("isEval")
	var isEval *int8 // 默认为nil，表示不筛选
	if isEvalStr != "" {
		if parsedisEval, err := strconv.Atoi(isEvalStr); err == nil {
			val := int8(parsedisEval)
			isEval = &val
		}
	}

	sourceStr := ctx.URLParam("source")
	var source *int8 // 默认为nil，表示不筛选
	if sourceStr != "" {
		if parsedSource, err := strconv.Atoi(sourceStr); err == nil {
			val := int8(parsedSource)
			source = &val
		}
	}

	// 解析时间范围参数
	const timeLayout = "2006-01-02 15:04:05"
	var startTime, endTime *time.Time

	startTimeStr := ctx.URLParam("startTime")
	if startTimeStr != "" {
		parsedTime, err := time.Parse(timeLayout, startTimeStr)
		if err == nil {
			startTime = &parsedTime
		}
	}

	endTimeStr := ctx.URLParam("endTime")
	if endTimeStr != "" {
		parsedTime, err := time.Parse(timeLayout, endTimeStr)
		if err == nil {
			endTime = &parsedTime
		}
	}

	resp, err := sandbox_service.GetAnnotations(rtxValues, appID, targetID, instruction,
		limit, offset, isDeleted, isEval, source, startTime, endTime)
	if err != nil {
		resp = &base.Resp[sandbox_api_model.GetAnnotationsRespData]{}
		resp.SetErr(ctx, iris.StatusInternalServerError, err)
		_ = ctx.JSON(resp)
		return
	}

	_ = ctx.JSON(resp)
}

// deleteAnnotation 软删除标注内容接口
func deleteAnnotation(ctx iris.Context) {
	req := &sandbox_api_model.DeleteAnnotationReq{}
	resp := &base.Resp[any]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	resp, err := sandbox_service.DeleteAnnotation(req)
	if err != nil {
		resp = &base.Resp[any]{}
		resp.SetErr(ctx, iris.StatusInternalServerError, err)
		_ = ctx.JSON(resp)
		return
	}

	_ = ctx.JSON(resp)
}

// deleteAnnotationOperation 软删除标注操作记录接口
func deleteAnnotationOperation(ctx iris.Context) {
	req := &sandbox_api_model.DeleteAnnotationOperationReq{}
	resp := &base.Resp[any]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	resp, err := sandbox_service.DeleteAnnotationOperation(req)
	if err != nil {
		resp = &base.Resp[any]{}
		resp.SetErr(ctx, iris.StatusInternalServerError, err)
		_ = ctx.JSON(resp)
		return
	}

	_ = ctx.JSON(resp)
}

func evalAnnotation(ctx iris.Context) {
	req := &sandbox_api_model.EvalAnnotationReq{}
	resp := &base.Resp[any]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	resp, err := sandbox_service.EvalAnnotation(req)
	if err != nil {
		resp = &base.Resp[any]{}
		resp.SetErr(ctx, iris.StatusInternalServerError, err)
		_ = ctx.JSON(resp)
		return
	}

	_ = ctx.JSON(resp)
}
