FROM mirrors.tencent.com/yuyiyiwang/yuyiyiwang_env:sandboxagentbase-v1.0.12
# 先拷贝文件
COPY ./etc /home/<USER>/mmfinderdrsandboxagentsvr/etc
COPY ./bin /home/<USER>/mmfinderdrsandboxagentsvr/bin
COPY ./sbin /home/<USER>/mmfinderdrsandboxagentsvr/sbin
COPY ./pyfile /home/<USER>/mmfinderdrsandboxagentsvr/pyfile
COPY ./start.sh /home/<USER>/workspace/start.sh
# 再修改权限
RUN chown -R root:users /home/<USER>/mmfinderdrsandboxagentsvr/etc && \
    chown -R root:users /home/<USER>/mmfinderdrsandboxagentsvr/bin && \
    chown -R root:users /home/<USER>/mmfinderdrsandboxagentsvr/sbin && \
    chown -R root:users /home/<USER>/mmfinderdrsandboxagentsvr/pyfile && \
    chown root:users /home/<USER>/workspace/start.sh
CMD ["/bin/bash"]