FROM mirrors.tencent.com/yuyiyiwang/yuyiyiwang_env:wmpf-proxy-app-v2.0.2
USER root
# 替换为腾讯云镜像源(适用于 Ubuntu 22.04 Jammy)
RUN cp /etc/apt/sources.list /etc/apt/sources.list.bak && \
    sed -i 's|http://.*archive.ubuntu.com|https://mirrors.cloud.tencent.com|g' /etc/apt/sources.list && \
    sed -i 's|http://.*security.ubuntu.com|https://mirrors.cloud.tencent.com|g' /etc/apt/sources.list
# 安装 Python 3.12 的环境
RUN apt-get update  && \
    # 安装编译 Python 的依赖  
    apt-get install -y build-essential zlib1g-dev libncurses5-dev libgdbm-dev libnss3-dev \
    libssl-dev libffi-dev libsqlite3-dev wget libbz2-dev liblzma-dev vim && \
    # 从 Python 官网下载源码编译安装
    wget https://www.python.org/ftp/python/3.12.0/Python-3.12.0.tgz && \
    tar -xzf Python-3.12.0.tgz && \
    cd Python-3.12.0 && \
    ./configure --enable-optimizations --enable-shared && \
    make -j$(nproc) && \
    make install && \
    # 手动创建共享库软链接
    ln -sf /usr/local/lib/libpython3.12.so.1.0 /usr/lib/ && \
    ldconfig && \
    cd .. && \
    rm -rf Python-3.12.0* && \
    # 创建必要的软链接
    ln -sf /usr/local/bin/python3.12 /usr/bin/python3 && \
    ln -sf /usr/local/bin/pip3.12 /usr/bin/pip3 && \
    # 安装必要的 pip 库
    python3 -m ensurepip --upgrade && \
    python3 -m pip install --upgrade pip setuptools wheel && \
    # 安装其他系统依赖
    apt-get install -y libaio1 && \
    # 确保 python3 指向 python3.12（使用正确的安装路径）
    update-alternatives --install /usr/bin/python3 python3 /usr/local/bin/python3.12 10 && \
    # 确保 pip3 指向正确的版本
    update-alternatives --install /usr/bin/pip3 pip3 /usr/local/bin/pip3.12 10 && \
    # 明确安装 supervisor 4.2.5 版本并创建系统链接
    pip3 install supervisor==4.2.5 && \
    ln -s /usr/local/bin/supervisord /usr/bin/supervisord && \
    ln -s /usr/local/bin/supervisorctl /usr/bin/supervisorctl && \
    mkdir -p /home/<USER>
    mv /root/.bashrc /home/<USER>
    # 安装其他 Python 依赖
    pip3 install --upgrade pip setuptools wheel && \
    pip3 install --upgrade pillow --index-url https://mirrors.cloud.tencent.com/pypi/simple && \
    pip3 install --upgrade py_mini_racer --index-url https://mirrors.cloud.tencent.com/pypi/simple && \
    pip3 install --upgrade requests --index-url https://mirrors.cloud.tencent.com/pypi/simple && \
    pip3 install wxms==1.0.9 --index-url https://yuyiyiwang:<EMAIL>/repository/pypi/tencent_pypi/simple && \
    pip3 install jinja2 --index-url https://mirrors.cloud.tencent.com/pypi/simple && \
    pip3 install lxml --index-url https://mirrors.cloud.tencent.com/pypi/simple && \
    pip3 install openai --index-url https://mirrors.cloud.tencent.com/pypi/simple
# 下载 miniprogram.so
# RUN wget --tries=3 --timeout=60 --user yuyiyiwang --password 96722916a0bc11efbf0e525400bd71fc \
#     https://mirrors.tencent.com/repository/generic/upload_yuyi/miniprogram.so && \
#     mv miniprogram.so /usr/local/lib/python3.12/site-packages/wxms/ && \
# 使用本地 miniprogram.so 文件
COPY miniprogram.so /usr/local/lib/python3.12/site-packages/wxms/
RUN  chmod 644 /usr/local/lib/python3.12/site-packages/wxms/miniprogram.so
CMD ["/bin/bash"]